import { useMemo, memo } from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { NewHistoryRecord } from '@/types/history';

interface ChartDataPoint {
  timestamp: string;
  time: string;
  binancePrice: number;
  backpackPrice: number;
  lighterMid: number;
}

interface PriceComparisonChartProps {
  data: NewHistoryRecord[];
  height?: number;
}

export const PriceComparisonChart = memo(function PriceComparisonChart({ data, height = 500 }: PriceComparisonChartProps) {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // 使用数据长度和第一条记录的时间戳作为稳定的标识
    const dataKey = `${data.length}-${data[0]?.timestamp}`;

    const processedData = data.map((record, index) => ({
      id: `${record.timestamp}-${index}`, // 添加稳定的ID
      timestamp: record.timestamp,
      time: record.timestamp.slice(5, 16).replace(' ', '\n'), // 简化时间格式，避免locale计算
      binancePrice: Number(record.binance_price.toFixed(1)),
      backpackPrice: Number(record.backpack_price.toFixed(1)),
      lighterMid: Number(record.lighter_mid.toFixed(1)),
    }));

    // 反转数组，让时间从左到右递增（最新时间在右边）
    const reversedData = [...processedData].reverse();

    // 如果数据量太大，进行稳定采样
    if (reversedData.length > 1000) {
      const step = Math.ceil(reversedData.length / 1000);
      const sampled = [];
      for (let i = 0; i < reversedData.length; i += step) {
        sampled.push(reversedData[i]);
      }
      return sampled;
    }

    return reversedData;
  }, [data.length, data[0]?.timestamp]);

  // 计算Y轴范围 - 基于平均价格 ±500
  const yAxisDomain = useMemo(() => {
    if (chartData.length === 0) return [89000, 91000]; // 默认范围

    // 收集所有价格数据
    const allPrices = chartData.flatMap(d => [d.binancePrice, d.backpackPrice, d.lighterMid]);

    // 计算平均价格
    const avgPrice = allPrices.reduce((sum, price) => sum + price, 0) / allPrices.length;

    // 设置范围为平均价格 ±500
    const range = 500;
    const minPrice = avgPrice - range;
    const maxPrice = avgPrice + range;

    console.log(`Price chart Y-axis: avg=${avgPrice.toFixed(1)}, range=[${minPrice.toFixed(1)}, ${maxPrice.toFixed(1)}]`);

    return [minPrice, maxPrice];
  }, [chartData]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">{label}</p>
          <div className="space-y-1 text-sm">
            {payload.map((entry: any, index: number) => (
              <div key={index} className="flex items-center gap-2">
                <div 
                  className="w-3 h-3 rounded" 
                  style={{ backgroundColor: entry.color }}
                ></div>
                <span>{entry.name}: <span className="font-medium">${entry.value.toFixed(1)}</span></span>
              </div>
            ))}
          </div>
        </div>
      );
    }
    return null;
  };

  if (chartData.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        暂无价格对比数据
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">价格对比图</h3>
        <div className="flex items-center gap-4 text-sm text-gray-600">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded"></div>
            <span>币安价格</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded"></div>
            <span>Backpack价格</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-orange-500 rounded"></div>
            <span>Lighter中间价</span>
          </div>
          <div className="text-xs text-gray-500">
            数据点: {chartData.length} / {data.length}
          </div>
        </div>
      </div>
      
      <ResponsiveContainer width="100%" height={height}>
        <LineChart
          data={chartData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 60,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="time"
            tick={{ fontSize: 12 }}
            angle={-45}
            textAnchor="end"
            height={60}
            interval="preserveStartEnd"
          />
          <YAxis 
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => `$${value.toLocaleString()}`}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          
          <Line
            type="monotone"
            dataKey="binancePrice"
            stroke="#3b82f6"
            strokeWidth={2}
            dot={false}
            name="币安价格"
            connectNulls={false}
          />
          <Line
            type="monotone"
            dataKey="backpackPrice"
            stroke="#10b981"
            strokeWidth={2}
            dot={false}
            name="Backpack价格"
            connectNulls={false}
          />
          <Line
            type="monotone"
            dataKey="lighterMid"
            stroke="#f59e0b"
            strokeWidth={2}
            dot={false}
            name="Lighter中间价"
            connectNulls={false}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
});

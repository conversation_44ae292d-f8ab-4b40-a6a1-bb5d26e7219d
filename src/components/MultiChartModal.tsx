import { useState, useEffect, useCallback, useRef } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  BarChart3,
  RefreshCw,
  TrendingUp,
  LineChart as LineChartIcon,
  Download
} from 'lucide-react';
import { NewHistoryResponse, NewHistoryRecord } from '@/types/history';
import { PriceComparisonChart } from '@/components/charts/PriceComparisonChart';
import { SpreadAnalysisChart } from '@/components/charts/SpreadAnalysisChart';
import { TimeRangeSelector } from '@/components/TimeRangeSelector';
import { toast } from 'sonner';

interface MultiChartModalProps {
  children: React.ReactNode;
}

export function MultiChartModal({ children }: MultiChartModalProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [historyData, setHistoryData] = useState<NewHistoryRecord[]>([]);
  const [activeTab, setActiveTab] = useState('spread-analysis');
  const [timeRange, setTimeRange] = useState({
    count: 1000,
    startTime: '',
    endTime: ''
  });

  // 使用ref来避免重复请求
  const isLoadingRef = useRef(false);
  const dataVersionRef = useRef(0);

  const fetchHistoryData = useCallback(async (params?: {
    count?: number;
    start_time?: string;
    end_time?: string;
  }) => {
    if (isLoadingRef.current) return; // 防止重复请求

    isLoadingRef.current = true;
    setLoading(true);

    try {
      const queryParams = new URLSearchParams();

      if (params?.count) {
        queryParams.append('count', params.count.toString());
      } else if (params?.start_time && params?.end_time) {
        queryParams.append('start_time', params.start_time);
        queryParams.append('end_time', params.end_time);
      } else {
        // 默认获取24小时数据
        queryParams.append('count', '1000');
      }

      const response = await fetch(
        `/api/history?${queryParams.toString()}`
      );

      if (!response.ok) {
        throw new Error('Failed to fetch history data');
      }

      const data: NewHistoryResponse = await response.json();

      // 增加数据版本号，用于强制重新渲染
      dataVersionRef.current += 1;
      setHistoryData(data.data || []);

      toast.success(`获取到 ${data.count} 条历史数据`);
    } catch (error) {
      console.error('Error fetching history data:', error);
      toast.error('获取历史数据失败');
    } finally {
      setLoading(false);
      isLoadingRef.current = false;
    }
  }, []);

  useEffect(() => {
    if (open && historyData.length === 0) {
      fetchHistoryData();
    }
  }, [open]);

  const handleTimeRangeChange = (range: {
    count?: number;
    startTime?: string;
    endTime?: string;
  }) => {
    setTimeRange(range);
    fetchHistoryData({
      count: range.count,
      start_time: range.startTime,
      end_time: range.endTime
    });
  };

  // 快速时间选择处理函数
  const handleQuickTimeSelect = (hours: number) => {
    const count = hours * 60; // 转换为分钟数
    const range = {
      count,
      startTime: '',
      endTime: ''
    };

    setTimeRange(range);
    fetchHistoryData({ count });
  };

  const handleExportData = () => {
    if (historyData.length === 0) {
      toast.error('暂无数据可导出');
      return;
    }

    const csvContent = [
      'timestamp,binance_price,backpack_price,lighter_bid,lighter_ask,lighter_mid,lighter_spread',
      ...historyData.map(record => 
        `${record.timestamp},${record.binance_price},${record.backpack_price},${record.lighter_bid},${record.lighter_ask},${record.lighter_mid},${record.lighter_spread}`
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `btc_history_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast.success('数据导出成功');
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-7xl max-h-[95vh] overflow-hidden">
        <DialogHeader className="pb-2">
          <DialogTitle className="flex items-center gap-1 text-base">
            <BarChart3 className="h-4 w-4" />
            BTC 价格历史分析
          </DialogTitle>
        </DialogHeader>
        


        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
          <TabsList className="grid w-full grid-cols-2 h-8">
            <TabsTrigger value="spread-analysis" className="flex items-center gap-1 text-xs h-6">
              <TrendingUp className="h-3 w-3" />
              价差分析
            </TabsTrigger>
            <TabsTrigger value="price-comparison" className="flex items-center gap-1 text-xs h-6">
              <LineChartIcon className="h-3 w-3" />
              价格对比
            </TabsTrigger>
          </TabsList>

          {/* 查询条件按钮区域 */}
          <div className="bg-gray-50 rounded p-2 mt-2 mb-2">
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <span className="text-xs font-medium text-gray-700">时间范围:</span>
                <div className="flex flex-wrap gap-1">
                  <Button
                    variant={timeRange.count === 60 ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleQuickTimeSelect(1)}
                    disabled={loading}
                    className="h-6 px-2 text-xs"
                  >
                    1小时
                  </Button>
                  <Button
                    variant={timeRange.count === 360 ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleQuickTimeSelect(6)}
                    disabled={loading}
                    className="h-6 px-2 text-xs"
                  >
                    6小时
                  </Button>
                  <Button
                    variant={timeRange.count === 1440 ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleQuickTimeSelect(24)}
                    disabled={loading}
                    className="h-6 px-2 text-xs"
                  >
                    1天
                  </Button>
                  <Button
                    variant={timeRange.count === 4320 ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleQuickTimeSelect(72)}
                    disabled={loading}
                    className="h-6 px-2 text-xs"
                  >
                    3天
                  </Button>
                  <Button
                    variant={timeRange.count === 10080 ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleQuickTimeSelect(168)}
                    disabled={loading}
                    className="h-6 px-2 text-xs"
                  >
                    7天
                  </Button>
                  <Button
                    variant={timeRange.count === 43200 ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleQuickTimeSelect(720)}
                    disabled={loading}
                    className="h-6 px-2 text-xs"
                  >
                    30天
                  </Button>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="text-xs text-gray-600">
                    共 {historyData.length} 条记录
                  </div>
                  {timeRange.startTime && timeRange.endTime && (
                    <div className="text-xs text-gray-500">
                      {timeRange.startTime} 至 {timeRange.endTime}
                    </div>
                  )}
                </div>

                <div className="flex items-center gap-1">
                  <Button
                    onClick={handleExportData}
                    disabled={loading || historyData.length === 0}
                    variant="outline"
                    size="sm"
                    className="h-6 px-2 text-xs"
                  >
                    <Download className="h-3 w-3 mr-1" />
                    导出
                  </Button>
                  <Button
                    onClick={() => fetchHistoryData()}
                    disabled={loading}
                    variant="outline"
                    size="sm"
                    className="h-6 px-2 text-xs"
                  >
                    <RefreshCw className={`h-3 w-3 mr-1 ${loading ? 'animate-spin' : ''}`} />
                    刷新
                  </Button>
                </div>
              </div>
            </div>
          </div>



          <div className="overflow-auto max-h-[60vh]">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <RefreshCw className="h-8 w-8 animate-spin mr-3" />
                <span className="text-lg">加载中...</span>
              </div>
            ) : historyData.length === 0 ? (
              <div className="text-center py-12 text-gray-500">
                <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg">暂无历史数据</p>
                <p className="text-sm">请选择时间范围或刷新数据</p>
              </div>
            ) : (
              <>
                <TabsContent value="spread-analysis" className="mt-0">
                  <SpreadAnalysisChart key={`spread-${historyData.length}`} data={historyData} />
                </TabsContent>

                <TabsContent value="price-comparison" className="mt-0">
                  <PriceComparisonChart key={`price-${historyData.length}`} data={historyData} />
                </TabsContent>
              </>
            )}
          </div>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}

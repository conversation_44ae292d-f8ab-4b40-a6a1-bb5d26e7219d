import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, <PERSON>alogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { History, RefreshCw, TrendingUp, TrendingDown, BarChart3, List } from 'lucide-react';
import { NewHistoryResponse, NewHistoryRecord } from '@/types/history';
import { PriceSpreadChart } from '@/components/PriceSpreadChart';
import { toast } from 'sonner';

interface HistoryModalProps {
  children: React.ReactNode;
}

export function HistoryModal({ children }: HistoryModalProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [historyData, setHistoryData] = useState<NewHistoryRecord[]>([]);
  const [viewMode, setViewMode] = useState<'chart' | 'list'>('chart');

  const fetchHistoryData = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/history?count=1000');
      if (!response.ok) {
        throw new Error('Failed to fetch history data');
      }
      const data: NewHistoryResponse = await response.json();
      setHistoryData(data.data || []);
      toast.success(`获取到 ${data.count} 条历史数据`);
    } catch (error) {
      console.error('Error fetching history data:', error);
      toast.error('获取历史数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open) {
      fetchHistoryData();
    }
  }, [open]);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 1,
      maximumFractionDigits: 1,
    }).format(price);
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('zh-CN');
  };

  const calculateSpread = (priceA: number, priceB: number) => {
    return priceA - priceB;
  };

  const getSpreadColor = (spread: number) => {
    if (spread > 0) return 'text-green-600';
    if (spread < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getSpreadIcon = (spread: number) => {
    if (spread > 0) return <TrendingUp className="h-3 w-3" />;
    if (spread < 0) return <TrendingDown className="h-3 w-3" />;
    return null;
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <History className="h-4 w-4" />
            BTC 价格历史数据
          </DialogTitle>
        </DialogHeader>

        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-4">
            <div className="text-sm text-gray-600">
              共 {historyData.length} 条记录
            </div>
            <div className="flex items-center gap-1 bg-gray-100 rounded-lg p-1">
              <Button
                onClick={() => setViewMode('chart')}
                variant={viewMode === 'chart' ? 'default' : 'ghost'}
                size="sm"
                className="h-6 px-2 text-xs"
              >
                <BarChart3 className="h-3 w-3 mr-1" />
                图表
              </Button>
              <Button
                onClick={() => setViewMode('list')}
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                className="h-6 px-2 text-xs"
              >
                <List className="h-3 w-3 mr-1" />
                列表
              </Button>
            </div>
          </div>
          <Button
            onClick={fetchHistoryData}
            disabled={loading}
            variant="outline"
            size="sm"
            className="h-7 px-2 text-xs"
          >
            <RefreshCw className={`h-3 w-3 mr-1 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>

        <div className="overflow-auto max-h-[70vh]">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin mr-2" />
              <span>加载中...</span>
            </div>
          ) : historyData.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              暂无历史数据
            </div>
          ) : viewMode === 'chart' ? (
            <PriceSpreadChart data={historyData} height={500} />
          ) : (
            <div className="space-y-3">
              {historyData.map((record, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm font-medium text-gray-700">
                      {formatTime(record.timestamp)}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                    <div className="text-center">
                      <div className="text-xs text-gray-500 mb-1">币安</div>
                      <div className="font-medium">{formatPrice(record.binance_price)}</div>
                    </div>
                    <div className="text-center">
                      <div className="text-xs text-gray-500 mb-1">Backpack</div>
                      <div className="font-medium">{formatPrice(record.backpack_price)}</div>
                    </div>
                    <div className="text-center">
                      <div className="text-xs text-gray-500 mb-1">Lighter</div>
                      <div className="font-medium">{formatPrice(record.lighter_mid)}</div>
                    </div>
                  </div>

                  <div className="border-t border-gray-200 pt-3">
                    <div className="text-xs text-gray-500 mb-2">价差 (vs Lighter)</div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">币安-Lighter:</span>
                        <div className={`flex items-center gap-1 font-medium ${getSpreadColor(calculateSpread(record.binance_price, record.lighter_mid))}`}>
                          {getSpreadIcon(calculateSpread(record.binance_price, record.lighter_mid))}
                          {formatPrice(calculateSpread(record.binance_price, record.lighter_mid))}
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Backpack-Lighter:</span>
                        <div className={`flex items-center gap-1 font-medium ${getSpreadColor(calculateSpread(record.backpack_price, record.lighter_mid))}`}>
                          {getSpreadIcon(calculateSpread(record.backpack_price, record.lighter_mid))}
                          {formatPrice(calculateSpread(record.backpack_price, record.lighter_mid))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
